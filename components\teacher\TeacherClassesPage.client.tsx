// components/teacher/TeacherClassesPage.client.tsx
'use client';

import TeacherClassesPageWithData from './TeacherClassesPageWithData.client';

// Mock data - in real implementation, this would come from the database
const mockClasses: any[] = [];
const mockGradeLevels: any[] = [];
const mockEducationalContexts: any[] = [];

const TeacherClassesPage = () => {
  return (
    <TeacherClassesPageWithData
      allClasses={mockClasses}
      allGradeLevels={mockGradeLevels}
      allEducationalContexts={mockEducationalContexts}
    />
  );
};

export default TeacherClassesPage;
